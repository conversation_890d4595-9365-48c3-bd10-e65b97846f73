:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
CLS
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (SET "__init_path__=%~1") ELSE (SET "__init_path__=%CD%")
IF "!__init_path__:~-1!"=="\" SET "__init_path__=!__init_path__:~0,-1!"

:: =============================================================================
:: robocopy: define arguments
:: =============================================================================
::
:: /COPY:DAT  - Copies Data, Attributes, Timestamps.
:: /DCOPY:T   - Copies directory Timestamps.
:: /E         - Copies subdirectories, including empty ones.
:: /V         - Verbose output (includes skipped files). Useful for logs.
:: /TS        - Includes source file Timestamps in output.
:: /FP        - Includes Full Path names of files in output.
:: /R:1       - Number of Retries on failed copies: 1.
:: /W:3       - Wait time between retries: 3 seconds.
SET "COPY_ARGS_SAFE=/COPY:DAT /DCOPY:T /E /V /TS /R:0 /W:3"

:: /XF        - Exclude Files matching these names/types.
:: /XD        - Exclude Directories matching these names.
:: Add or remove exclusions as needed.
SET "COPY_ARGS_SAFE=%COPY_ARGS_SAFE% /XF *.tmp *.pyc /XD .venv venv node_modules __pycache__"

:: /LOG       - Create new log file (using unique name).
:: /NS        - No Size - don't log file sizes.
:: /TEE       - Outputs to console window as well as log file.
:: Uncomment the line below to enable logging
:: SET "COPY_ARGS_SAFE=%COPY_ARGS_SAFE% /NS /LOG:robocopy_log.txt /TEE"


:: =============================================================================
:: cmd: prompt for destination path
:: =============================================================================
:ProcessDestinationPath
    ECHO.
    SET "__destination__="
    SET /P "__destination__=Enter the destination path:"
    FOR %%P IN ("!__init_path__!\..") DO SET "__parent_path__=%%~fP"
    IF NOT EXIST "!__destination__!" (SET "__destination__=!__parent_path__!\!__destination__!") ELSE (SET "__destination__=!__destination__!")


:: =============================================================================
:: robocopy: execute
:: =============================================================================
:ExecuteRobocopy
    SET timer_start=%time:~0,8%
    ECHO.
    ECHO ============================================================================
    ECHO Starting Robocopy...
    ECHO.
    ECHO    Source: !__init_path!
    ECHO Destination: !__destination__!
    ECHO.
    ECHO Command: ROBOCOPY "!__init_path__!" "!__destination__!" %COPY_ARGS_SAFE%
    ECHO ============================================================================
    ECHO.

    ROBOCOPY "!__init_path__!" "!__destination__!" %COPY_ARGS_SAFE%

    SET timer_end=%time:~0,8%
    ECHO.
    ECHO ============================================================================
    ECHO Robocopy finished.
    ECHO.
    ECHO - Start time : %timer_start%
    ECHO - End time   : %timer_end%
    ECHO ============================================================================
    ECHO.

    GOTO EndScript

:: =============================================================================
:: cmd: exit
:: =============================================================================
:EndScript
    ECHO Press any key to close this window...
    PAUSE > NUL
    EXIT /B
