# Git Commit History Graph 
 
Generated on: 2025-03-27 13:49:47 
 
## Commit: 94eea87 - (3 weeks ago) ++chk - LAP (HEAD -> master) 
 
### Changed Files: 
 
```diff 
- (Renamed from) exe/LIB/bat/py_venv_run_script.bat	exe/LIB/bat/py_venv_execute.bat 
+ (Added) exe/LIB/bat/py_venv_git_commit_all.bat 
~ (Modified) exe/LIB/bat/py_venv_terminal.bat 
+ (Added) exe/NSS/_3_items/itm_bat_pyvenvexecute.nss 
+ (Added) exe/NSS/_3_items/itm_py_youtubeplaylistexporter.nss 
~ (Modified) exe/NSS/_4_groups/grp_jorn_actions_git.nss 
~ (Modified) exe/NSS/_5_menus/mnu_actions_user_create.nss 
~ (Modified) exe/NSS/_5_menus/mnu_user_jorn_scripts.nss 
~ (Modified) exe/NSS/_5_menus/mnu_user_jorn_scripts_python.nss 
~ (Modified) exe/NSS/_5_menus/wip_menus/mnu_Processes.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
``` 
 
| 
| 
 
## Commit: 95c84b5 - (4 weeks ago) chk - LAP 
 
### Changed Files: 
 
```diff 
+ (Added) exe/NSS/_3_items/itm_app_mpvplayer.nss 
~ (Modified) exe/NSS/_4_groups/grp_jorn_actions_git.nss 
~ (Modified) exe/NSS/_4_groups/grp_jorn_dirs_nas_flow.nss 
~ (Modified) exe/NSS/_5_menus/mnu_scratchpad.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
``` 
 
| 
| 
 
## Commit: 8569e7e - (6 weeks ago) chk - LAP 
 
### Changed Files: 
 
```diff 
+ (Added) exe/NSS/_1_init/_1_init.md 
+ (Added) exe/NSS/_3_items/_3_items.md 
~ (Modified) exe/NSS/_3_items/itm_app_everything.nss 
~ (Modified) exe/NSS/_3_items/itm_app_sysinternals_autoruns.nss 
~ (Modified) exe/NSS/_3_items/itm_app_sysinternals_diskmon.nss 
~ (Modified) exe/NSS/_3_items/itm_app_sysinternals_procexp.nss 
+ (Added) exe/NSS/_3_items/itm_app_sysinternals_procmon.nss 
~ (Modified) exe/NSS/_3_items/itm_app_sysinternals_tcpview.nss 
+ (Added) exe/NSS/_3_items/itm_app_trafficmonitor.nss 
~ (Modified) exe/NSS/_3_items/itm_py_audioandvideocombiner.nss 
+ (Added) exe/NSS/_3_items/itm_py_audiofromvideoextractor.nss 
~ (Modified) exe/NSS/_3_items/itm_py_closeduplicatewindows.nss 
+ (Added) exe/NSS/_4_groups/_4_groups.md 
~ (Modified) exe/NSS/_4_groups/grp_jorn_apps_sysinternals.nss 
~ (Modified) exe/NSS/_5_menus/mnu_actions_user_create.nss 
~ (Modified) exe/NSS/_5_menus/mnu_scratchpad.nss 
~ (Modified) exe/NSS/_5_menus/mnu_user_jorn_apps.nss 
~ (Modified) exe/NSS/_5_menus/mnu_user_jorn_scripts.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_titlebar.nss 
+ (Added) exe/exe.md 
``` 
 
| 
| 
 
## Commit: f861374 - (2 months ago) chk - DSK 
 
### Changed Files: 
 
```diff 
+ (Added) exe/NSS/_3_items/itm_app_aftereffects.nss 
~ (Modified) exe/NSS/_3_items/itm_app_everything.nss 
+ (Added) exe/NSS/_3_items/itm_app_sublimemerge.nss 
~ (Modified) exe/NSS/_3_items/itm_py_speechtotext.nss 
~ (Modified) exe/NSS/_4_groups/grp_jorn_dirs_nas_flow.nss 
~ (Modified) exe/NSS/_5_menus/mnu_scratchpad.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
``` 
 
| 
| 
 
## Commit: f172774 - (3 months ago) keys:chk - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_1_init/_a_constants/def_keys.nss 
~ (Modified) exe/NSS/_3_items/itm_app_gitrestoremtime.nss 
~ (Modified) exe/NSS/_4_groups/grp_jorn_actions_git.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar_nuc.nss 
``` 
 
| 
| 
 
## Commit: 9296691 - (3 months ago) chk - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_3_items/itm_app_comfyui.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_titlebar.nss 
``` 
 
| 
| 
 
## Commit: a0fb2bc - (3 months ago) keys:comments - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_1_init/_a_constants/def_keys.nss 
``` 
 
| 
| 
 
## Commit: 3ab5364 - (3 months ago) keys:chk - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_1_init/_a_constants/def_icons.nss 
~ (Modified) exe/NSS/_1_init/_a_constants/def_keys.nss 
~ (Modified) exe/NSS/_3_items/itm_app_gitrestoremtime.nss 
~ (Modified) exe/NSS/_4_groups/grp_jorn_actions_git.nss 
~ (Modified) exe/NSS/_5_menus/mnu_sys_debug_commands.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar_nuc.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_titlebar.nss 
~ (Modified) exe/shell.nss 
+ (Added) user/versions/nss_v3_backup_2024.12.29.7z 
``` 
 
| 
| 
 
## Commit: 75b1df7 - (3 months ago) v3:wip - DSK 
 
### Changed Files: 
 
```diff 
- (Renamed from) exe/NSS/_1_init/_a_defaults/def_bools.nss	exe/NSS/_1_init/_a_constants/def_bools.nss 
- (Renamed from) exe/NSS/_1_init/_a_defaults/def_colors.nss	exe/NSS/_1_init/_a_constants/def_colors.nss 
- (Renamed from) exe/NSS/_1_init/_a_defaults/def_icons.nss	exe/NSS/_1_init/_a_constants/def_icons.nss 
- (Renamed from) exe/NSS/_1_init/_a_defaults/def_keys.nss	exe/NSS/_1_init/_a_constants/def_keys.nss 
- (Renamed from) exe/NSS/_1_init/_a_defaults/def_paths.nss	exe/NSS/_1_init/_a_constants/def_paths.nss 
- (Renamed from) exe/NSS/_1_init/_a_defaults/def_tooltips.nss	exe/NSS/_1_init/_a_constants/def_tooltips.nss 
- (Renamed from) exe/NSS/_1_init/_a_defaults/def_uris.nss	exe/NSS/_1_init/_a_constants/def_uris.nss 
- (Renamed from) exe/NSS/_3_items/system_actions/itm_action_sys_copypath.nss	exe/NSS/_3_items/itm_action_sys_copypath.nss 
- (Renamed from) exe/NSS/_3_items/system_actions/itm_action_sys_createtxt.nss	exe/NSS/_3_items/itm_action_sys_createtxt.nss 
- (Renamed from) exe/NSS/_3_items/system_actions/itm_action_sys_restartexplorer.nss	exe/NSS/_3_items/itm_action_sys_restartexplorer.nss 
- (Renamed from) exe/NSS/_3_items/system_actions/itm_action_sys_showdesktop.nss	exe/NSS/_3_items/itm_action_sys_showdesktop.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_3dsmax.nss	exe/NSS/_3_items/itm_app_3dsmax.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_audacity.nss	exe/NSS/_3_items/itm_app_audacity.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_bambustudio.nss	exe/NSS/_3_items/itm_app_bambustudio.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_beyondcompare.nss	exe/NSS/_3_items/itm_app_beyondcompare.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_blender.nss	exe/NSS/_3_items/itm_app_blender.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_bulkrenameutility.nss	exe/NSS/_3_items/itm_app_bulkrenameutility.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_claude.nss	exe/NSS/_3_items/itm_app_claude.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_comfyui.nss	exe/NSS/_3_items/itm_app_comfyui.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_cursor.nss	exe/NSS/_3_items/itm_app_cursor.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_everything.nss	exe/NSS/_3_items/itm_app_everything.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_filezilla.nss	exe/NSS/_3_items/itm_app_filezilla.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_firefox.nss	exe/NSS/_3_items/itm_app_firefox.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_gitrestoremtime.nss	exe/NSS/_3_items/itm_app_gitrestoremtime.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_gpt4all.nss	exe/NSS/_3_items/itm_app_gpt4all.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_gsmartcontrol.nss	exe/NSS/_3_items/itm_app_gsmartcontrol.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_irfanview.nss	exe/NSS/_3_items/itm_app_irfanview.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_kdenlive.nss	exe/NSS/_3_items/itm_app_kdenlive.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_libreoffice.nss	exe/NSS/_3_items/itm_app_libreoffice.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_losslesscut.nss	exe/NSS/_3_items/itm_app_losslesscut.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_mypyinterface.nss	exe/NSS/_3_items/itm_app_mypyinterface.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_nirsoft_regfromapp.nss	exe/NSS/_3_items/itm_app_nirsoft_regfromapp.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_nirsoft_registrychangesview.nss	exe/NSS/_3_items/itm_app_nirsoft_registrychangesview.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_nirsoft_regscanner.nss	exe/NSS/_3_items/itm_app_nirsoft_regscanner.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_nirsoft_winexplorer.nss	exe/NSS/_3_items/itm_app_nirsoft_winexplorer.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_notepad++.nss	exe/NSS/_3_items/itm_app_notepad++.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_pdf24.nss	exe/NSS/_3_items/itm_app_pdf24.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_photoshop.nss	exe/NSS/_3_items/itm_app_photoshop.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_powertoys.nss	exe/NSS/_3_items/itm_app_powertoys.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_qbittorrent.nss	exe/NSS/_3_items/itm_app_qbittorrent.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_rufus.nss	exe/NSS/_3_items/itm_app_rufus.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_rustdesk.nss	exe/NSS/_3_items/itm_app_rustdesk.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_sharex.nss	exe/NSS/_3_items/itm_app_sharex.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_simplewall.nss	exe/NSS/_3_items/itm_app_simplewall.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_sourcetree.nss	exe/NSS/_3_items/itm_app_sourcetree.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_sublimetext.nss	exe/NSS/_3_items/itm_app_sublimetext.nss 
- (Renamed from) exe/NSS/_3_items/system_apps/itm_app_sys_calc.nss	exe/NSS/_3_items/itm_app_sys_calc.nss 
- (Renamed from) exe/NSS/_3_items/system_apps/itm_app_sys_cmd.nss	exe/NSS/_3_items/itm_app_sys_cmd.nss 
- (Renamed from) exe/NSS/_3_items/system_apps/itm_app_sys_magnify.nss	exe/NSS/_3_items/itm_app_sys_magnify.nss 
- (Renamed from) exe/NSS/_3_items/system_apps/itm_app_sys_mspaint.nss	exe/NSS/_3_items/itm_app_sys_mspaint.nss 
- (Renamed from) exe/NSS/_3_items/system_apps/itm_app_sys_notepad.nss	exe/NSS/_3_items/itm_app_sys_notepad.nss 
- (Renamed from) exe/NSS/_3_items/system_apps/itm_app_sys_osk.nss	exe/NSS/_3_items/itm_app_sys_osk.nss 
- (Renamed from) exe/NSS/_3_items/system_apps/itm_app_sys_powershell.nss	exe/NSS/_3_items/itm_app_sys_powershell.nss 
- (Renamed from) exe/NSS/_3_items/system_apps/itm_app_sys_powershellise.nss	exe/NSS/_3_items/itm_app_sys_powershellise.nss 
- (Renamed from) exe/NSS/_3_items/system_apps/itm_app_sys_regedit.nss	exe/NSS/_3_items/itm_app_sys_regedit.nss 
- (Renamed from) exe/NSS/_3_items/system_apps/itm_app_sys_taskmanager.nss	exe/NSS/_3_items/itm_app_sys_taskmanager.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_sysinternals_autoruns.nss	exe/NSS/_3_items/itm_app_sysinternals_autoruns.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_sysinternals_diskmon.nss	exe/NSS/_3_items/itm_app_sysinternals_diskmon.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_sysinternals_procexp.nss	exe/NSS/_3_items/itm_app_sysinternals_procexp.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_sysinternals_tcpview.nss	exe/NSS/_3_items/itm_app_sysinternals_tcpview.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_telegram.nss	exe/NSS/_3_items/itm_app_telegram.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_vlc.nss	exe/NSS/_3_items/itm_app_vlc.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_winmerge.nss	exe/NSS/_3_items/itm_app_winmerge.nss 
- (Renamed from) exe/NSS/_3_items/user_apps/itm_app_yvonne.nss	exe/NSS/_3_items/itm_app_yvonne.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_bat_pyvenvterminal.nss	exe/NSS/_3_items/itm_bat_pyvenvterminal.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_bat_pyvenvwriterequirements.nss	exe/NSS/_3_items/itm_bat_pyvenvwriterequirements.nss 
- (Renamed from) exe/NSS/_3_items/user_dirs/itm_dir_jorn_nss.nss	exe/NSS/_3_items/itm_dir_jorn_nss.nss 
- (Renamed from) exe/NSS/_3_items/user_dirs/itm_dir_jorn_scratch.nss	exe/NSS/_3_items/itm_dir_jorn_scratch.nss 
- (Renamed from) exe/NSS/_3_items/user_dirs/itm_dir_jorn_share.nss	exe/NSS/_3_items/itm_dir_jorn_share.nss 
- (Renamed from) exe/NSS/_3_items/system_dirs/itm_dir_sys_appdata.nss	exe/NSS/_3_items/itm_dir_sys_appdata.nss 
- (Renamed from) exe/NSS/_3_items/system_dirs/itm_dir_sys_desktop.nss	exe/NSS/_3_items/itm_dir_sys_desktop.nss 
- (Renamed from) exe/NSS/_3_items/system_dirs/itm_dir_sys_documents.nss	exe/NSS/_3_items/itm_dir_sys_documents.nss 
- (Renamed from) exe/NSS/_3_items/system_dirs/itm_dir_sys_downloads.nss	exe/NSS/_3_items/itm_dir_sys_downloads.nss 
- (Renamed from) exe/NSS/_3_items/system_dirs/itm_dir_sys_programfiles.nss	exe/NSS/_3_items/itm_dir_sys_programfiles.nss 
- (Renamed from) exe/NSS/_3_items/system_dirs/itm_dir_sys_recent.nss	exe/NSS/_3_items/itm_dir_sys_recent.nss 
- (Renamed from) exe/NSS/_3_items/system_dirs/itm_dir_sys_recyclebin.nss	exe/NSS/_3_items/itm_dir_sys_recyclebin.nss 
- (Renamed from) exe/NSS/_3_items/system_dirs/itm_dir_sys_startup.nss	exe/NSS/_3_items/itm_dir_sys_startup.nss 
- (Renamed from) exe/NSS/_3_items/system_dirs/itm_dir_sys_thispc.nss	exe/NSS/_3_items/itm_dir_sys_thispc.nss 
- (Renamed from) exe/NSS/_3_items/system_dirs/itm_dir_sys_userprofile.nss	exe/NSS/_3_items/itm_dir_sys_userprofile.nss 
- (Renamed from) exe/NSS/_3_items/system_dirs/itm_dir_sys_userprofile_ssh.nss	exe/NSS/_3_items/itm_dir_sys_userprofile_ssh.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_audioandvideocombiner.nss	exe/NSS/_3_items/itm_py_audioandvideocombiner.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_bookmarkfolderizer.nss	exe/NSS/_3_items/itm_py_bookmarkfolderizer.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_closeduplicatewindows.nss	exe/NSS/_3_items/itm_py_closeduplicatewindows.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_environmentvariablesmanager.nss	exe/NSS/_3_items/itm_py_environmentvariablesmanager.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_gitfilterrepo.nss	exe/NSS/_3_items/itm_py_gitfilterrepo.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_gitsizeanalyzer.nss	exe/NSS/_3_items/itm_py_gitsizeanalyzer.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_markdowngenerator.nss	exe/NSS/_3_items/itm_py_markdowngenerator.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_projectgenerator.nss	exe/NSS/_3_items/itm_py_projectgenerator.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_renamewitheditor.nss	exe/NSS/_3_items/itm_py_renamewitheditor.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_sanitizefilenames.nss	exe/NSS/_3_items/itm_py_sanitizefilenames.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_speechtotext.nss	exe/NSS/_3_items/itm_py_speechtotext.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_urlgenerator.nss	exe/NSS/_3_items/itm_py_urlgenerator.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_videocompressor.nss	exe/NSS/_3_items/itm_py_videocompressor.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_videosplitter.nss	exe/NSS/_3_items/itm_py_videosplitter.nss 
- (Renamed from) exe/NSS/_3_items/user_utils/itm_py_win4ro1.nss	exe/NSS/_3_items/itm_py_win4ro1.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_windowutils.nss	exe/NSS/_3_items/itm_py_windowutils.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_youtubedownloader.nss	exe/NSS/_3_items/itm_py_youtubedownloader.nss 
- (Renamed from) exe/NSS/_3_items/user_scripts/itm_py_youtubeplaylistmanager.nss	exe/NSS/_3_items/itm_py_youtubeplaylistmanager.nss 
- (Renamed from) exe/NSS/_3_items/registry_commands/itm_reg_setkeyboardrepeatrate.nss	exe/NSS/_3_items/itm_reg_setkeyboardrepeatrate.nss 
- (Renamed from) exe/NSS/_3_items/registry_commands/itm_reg_settaskbarleft.nss	exe/NSS/_3_items/itm_reg_settaskbarleft.nss 
- (Deleted) exe/NSS/_3_items/user_apps/.backups/itm_app_comfyui/itm_app_comfyui__001__2024.11.23__15.30.nss 
- (Deleted) exe/NSS/_3_items/user_apps/.backups/itm_app_comfyui/itm_app_comfyui__002__2024.11.23__15.41.nss 
- (Renamed from) exe/NSS/_4_groups/jorn_actions/grp_actions_user_create.nss	exe/NSS/_4_groups/grp_actions_user_create.nss 
- (Renamed from) exe/NSS/_4_groups/jorn_actions/grp_actions_user_processes.nss	exe/NSS/_4_groups/grp_actions_user_processes.nss 
- (Renamed from) exe/NSS/_4_groups/user_debug/grp_debug_user_shell.nss	exe/NSS/_4_groups/grp_debug_user_shell.nss 
- (Renamed from) exe/NSS/_4_groups/jorn_actions/grp_jorn_actions_git.nss	exe/NSS/_4_groups/grp_jorn_actions_git.nss 
- (Renamed from) exe/NSS/_4_groups/jorn_apps/grp_jorn_apps_nirsoft.nss	exe/NSS/_4_groups/grp_jorn_apps_nirsoft.nss 
- (Renamed from) exe/NSS/_4_groups/jorn_apps/grp_jorn_apps_sysinternals.nss	exe/NSS/_4_groups/grp_jorn_apps_sysinternals.nss 
- (Renamed from) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_git_portal.nss	exe/NSS/_4_groups/grp_jorn_dirs_git_portal.nss 
- (Renamed from) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_android.nss	exe/NSS/_4_groups/grp_jorn_dirs_nas_android.nss 
- (Renamed from) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_cloud.nss	exe/NSS/_4_groups/grp_jorn_dirs_nas_cloud.nss 
- (Renamed from) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_flow.nss	exe/NSS/_4_groups/grp_jorn_dirs_nas_flow.nss 
- (Renamed from) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_networkdrives.nss	exe/NSS/_4_groups/grp_jorn_dirs_nas_networkdrives.nss 
- (Renamed from) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_work.nss	exe/NSS/_4_groups/grp_jorn_dirs_nas_work.nss 
- (Renamed from) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_workflow.nss	exe/NSS/_4_groups/grp_jorn_dirs_nas_workflow.nss 
- (Renamed from) exe/NSS/_4_groups/jorn_urls/grp_jorn_urls_gpts.nss	exe/NSS/_4_groups/grp_jorn_urls_gpts.nss 
- (Renamed from) exe/NSS/_4_groups/jorn_urls/grp_jorn_urls_websites.nss	exe/NSS/_4_groups/grp_jorn_urls_websites.nss 
- (Renamed from) exe/NSS/_4_groups/user_debug/grp_sys_debug_commands.nss	exe/NSS/_4_groups/grp_sys_debug_commands.nss 
- (Renamed from) exe/NSS/_4_groups/user_debug/grp_sys_debug_tooltips.nss	exe/NSS/_4_groups/grp_sys_debug_tooltips.nss 
- (Renamed from) exe/NSS/_4_groups/system_folders/grp_sys_dirs_cloud.nss	exe/NSS/_4_groups/grp_sys_dirs_cloud.nss 
- (Renamed from) exe/NSS/_4_groups/system_folders/grp_sys_dirs_common.nss	exe/NSS/_4_groups/grp_sys_dirs_common.nss 
- (Deleted) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_actions_git.nss 
- (Deleted) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_apps.nss 
- (Deleted) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_apps_git.nss 
- (Deleted) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_apps_nirsoft.nss 
- (Renamed from) exe/NSS/_5_menus/user_menus/mnu_actions_user_create.nss	exe/NSS/_5_menus/mnu_actions_user_create.nss 
- (Renamed from) exe/NSS/_5_menus/system_menus/mnu_apps_microsoft.nss	exe/NSS/_5_menus/mnu_apps_microsoft.nss 
- (Renamed from) exe/NSS/_5_menus/jorn_menus/mnu_scratchpad.nss	exe/NSS/_5_menus/mnu_scratchpad.nss 
- (Renamed from) exe/NSS/_5_menus/system_menus/mnu_sys_actions_clipboard.nss	exe/NSS/_5_menus/mnu_sys_actions_clipboard.nss 
- (Renamed from) exe/NSS/_5_menus/system_menus/mnu_sys_actions_setpeferences.nss	exe/NSS/_5_menus/mnu_sys_actions_setpeferences.nss 
- (Renamed from) exe/NSS/_5_menus/system_menus/mnu_sys_actions_windows.nss	exe/NSS/_5_menus/mnu_sys_actions_windows.nss 
- (Renamed from) exe/NSS/_5_menus/system_menus/mnu_sys_debug_commands.nss	exe/NSS/_5_menus/mnu_sys_debug_commands.nss 
- (Renamed from) exe/NSS/_5_menus/system_menus/mnu_sys_dirs_common.nss	exe/NSS/_5_menus/mnu_sys_dirs_common.nss 
- (Renamed from) exe/NSS/_5_menus/user_menus/mnu_user_apps_shell.nss	exe/NSS/_5_menus/mnu_user_apps_shell.nss 
+ (Added) exe/NSS/_5_menus/mnu_user_jorn_actions_git.nss 
+ (Added) exe/NSS/_5_menus/mnu_user_jorn_apps.nss 
- (Renamed from) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_apps_7zip.nss	exe/NSS/_5_menus/mnu_user_jorn_apps_7zip.nss 
- (Renamed from) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_apps_everything.nss	exe/NSS/_5_menus/mnu_user_jorn_apps_everything.nss 
+ (Added) exe/NSS/_5_menus/mnu_user_jorn_apps_git.nss 
+ (Added) exe/NSS/_5_menus/mnu_user_jorn_apps_nirsoft.nss 
- (Renamed from) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_dirs.nss	exe/NSS/_5_menus/mnu_user_jorn_dirs.nss 
- (Renamed from) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_dirs_common.nss	exe/NSS/_5_menus/mnu_user_jorn_dirs_common.nss 
- (Renamed from) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_scripts.nss	exe/NSS/_5_menus/mnu_user_jorn_scripts.nss 
- (Renamed from) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_scripts_python.nss	exe/NSS/_5_menus/mnu_user_jorn_scripts_python.nss 
- (Renamed from) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_urls.nss	exe/NSS/_5_menus/mnu_user_jorn_urls.nss 
- (Renamed from) exe/NSS/_5_menus/jorn_menus/mnu_user_nuc_urls.nss	exe/NSS/_5_menus/mnu_user_nuc_urls.nss 
- (Renamed from) exe/NSS/_5_menus/jorn_menus/mnu_user_projects.nss	exe/NSS/_5_menus/mnu_user_projects.nss 
~ (Modified) exe/NSS/_5_menus/wip_menus/mnu_Debugging.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_desktop.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_everything64.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar_nuc.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_titlebar.nss 
~ (Modified) exe/shell.nss 
- (Renamed from) exe/NSS/_1_init/_a_defaults/def_keys_prompt.nss.md	user/def_keys_prompt.nss.md 
+ (Added) user/versions/nss_v2_backup_2024.12.29.7z 
``` 
 
| 
| 
 
## Commit: 059fbdd - (3 months ago) chk - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_cursor.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_firefox.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_gpt4all.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_closeduplicatewindows.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_scripts.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
``` 
 
| 
| 
 
## Commit: e3d446e - (3 months ago) no message - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) __SHELL__.sublime-project 
``` 
 
| 
| 
 
## Commit: f45b83e - (3 months ago) no message - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) requirements.txt 
``` 
 
| 
| 
 
## Commit: f04bf77 - (3 months ago) init-py-env - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) .gitignore 
~ (Modified) __SHELL__.sublime-project 
+ (Added) __meta__/.cmd/py_venv_pip_devmode.bat 
+ (Added) __meta__/.cmd/py_venv_pip_install.bat 
+ (Added) __meta__/.cmd/py_venv_run_script.bat 
+ (Added) __meta__/.cmd/py_venv_terminal.bat 
+ (Added) __meta__/.cmd/py_venv_upgrade_requirements.bat 
+ (Added) __meta__/.cmd/py_venv_write_requirements.bat 
+ (Added) main.bat 
+ (Added) main.py 
+ (Added) py_venv_init.bat 
+ (Added) requirements.txt 
- (Renamed from) exe/.backups/nss_v1_backup_2024.08.31.7z	user/versions/nss_v1_backup_2024.08.31.7z 
+ (Added) user/versions/nss_v2_backup_2024.12.25.7z 
``` 
 
| 
| 
 
## Commit: c1c51aa - (3 months ago) wip - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_1_init/_a_defaults/def_colors.nss 
~ (Modified) exe/NSS/_3_items/user_dirs/itm_dir_jorn_nss.nss 
~ (Modified) exe/NSS/_3_items/user_dirs/itm_dir_jorn_scratch.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
``` 
 
| 
| 
 
## Commit: e1d1e0b - (3 months ago) chk - DSK 
 
### Changed Files: 
 
```diff 
+ (Added) exe/NSS/_3_items/user_dirs/itm_dir_jorn_nss.nss 
~ (Modified) exe/NSS/_3_items/user_scripts/itm_py_renamewitheditor.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
``` 
 
| 
| 
 
## Commit: ec09b2a - (3 months ago) chk - DSK 
 
### Changed Files: 
 
```diff 
- (Renamed from) exe/LIB/py_venv_pip_install.bat	exe/LIB/bat/py_venv_pip_install.bat 
- (Renamed from) exe/LIB/py_venv_run_script.bat	exe/LIB/bat/py_venv_run_script.bat 
- (Renamed from) exe/LIB/py_venv_terminal.bat	exe/LIB/bat/py_venv_terminal.bat 
- (Renamed from) exe/LIB/py_venv_upgrade_requirements.bat	exe/LIB/bat/py_venv_upgrade_requirements.bat 
- (Renamed from) exe/LIB/py_venv_write_requirements.bat	exe/LIB/bat/py_venv_write_requirements.bat 
+ (Added) exe/LIB/png/ico_hbomax.png 
+ (Added) exe/LIB/png/ico_imdb.png 
+ (Added) exe/LIB/png/ico_iptorrents.png 
+ (Added) exe/LIB/png/ico_netflix.png 
+ (Added) exe/LIB/png/ico_nrktv.png 
+ (Added) exe/LIB/png/ico_sidereel.png 
+ (Added) exe/LIB/png/ico_streaming.png 
+ (Added) exe/LIB/png/ico_youtube.png 
+ (Added) exe/LIB/svg/sublime.svg 
~ (Modified) exe/NSS/_1_init/_a_defaults/def_icons.nss 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_audacity.nss 
~ (Modified) exe/NSS/_3_items/user_scripts/itm_bat_pyvenvterminal.nss 
~ (Modified) exe/NSS/_3_items/user_scripts/itm_bat_pyvenvwriterequirements.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_audioandvideocombiner.nss 
~ (Modified) exe/NSS/_3_items/user_scripts/itm_py_renamewitheditor.nss 
~ (Modified) exe/NSS/_3_items/user_scripts/itm_py_speechtotext.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_videosplitter.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_scratchpad.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_apps.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_scripts.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_scripts_python.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_urls.nss 
+ (Added) exe/NSS/_5_menus/jorn_menus/mnu_user_nuc_urls.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
+ (Added) exe/NSS/_6_contexts/ctx_taskbar_nuc.nss 
~ (Modified) exe/shell.nss 
``` 
 
| 
| 
 
## Commit: 57b1553 - (3 months ago) chk - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) __SHELL__.sublime-project 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_mypyinterface.nss 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_powertoys.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_rufus.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_yvonne.nss 
~ (Modified) exe/NSS/_3_items/user_scripts/itm_py_projectgenerator.nss 
~ (Modified) exe/NSS/_4_groups/jorn_urls/grp_jorn_urls_gpts.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_scratchpad.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
- (Deleted) user/unsorted/old/nilesoftshell_chatgpt/2023.11.28_chatgpt.html 
``` 
 
| 
| 
 
## Commit: 6bf098c - (4 months ago) no message - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_1_init/_a_defaults/def_keys.nss 
``` 
 
| 
| 
 
## Commit: d25597f - (4 months ago) cursor, portable - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_cursor.nss 
``` 
 
| 
| 
 
## Commit: 3ca32cb - (4 months ago) no message - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_1_init/_a_defaults/def_keys.nss 
``` 
 
| 
| 
 
## Commit: e09ec21 - (4 months ago) keyboard modifiers - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_1_init/_a_defaults/def_keys.nss 
+ (Added) exe/NSS/_1_init/_a_defaults/def_keys_prompt.nss.md 
``` 
 
| 
| 
 
## Commit: 30aeaf6 - (4 months ago) added claud and cursor - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_claude.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_cursor.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
``` 
 
| 
| 
 
## Commit: 025dea4 - (4 months ago) chk - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_1_init/_a_defaults/def_keys.nss 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_3dsmax.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_claude.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_photoshop.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
``` 
 
| 
| 
 
## Commit: 19da24c - (4 months ago) chk - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/LIB/py_venv_terminal.bat 
~ (Modified) exe/LIB/py_venv_write_requirements.bat 
+ (Added) exe/NSS/_3_items/user_apps/.backups/itm_app_comfyui/itm_app_comfyui__001__2024.11.23__15.30.nss 
+ (Added) exe/NSS/_3_items/user_apps/.backups/itm_app_comfyui/itm_app_comfyui__002__2024.11.23__15.41.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_comfyui.nss 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_sublimetext.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_renamewitheditor.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_scratchpad.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_scripts.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
``` 
 
| 
| 
 
## Commit: bc7541f - (4 months ago) nss:updates - DSK 
 
### Changed Files: 
 
```diff 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_bambustudio.nss 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_vlc.nss 
~ (Modified) exe/NSS/_4_groups/system_folders/grp_sys_dirs_common.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_scratchpad.nss 
~ (Modified) exe/NSS/_5_menus/system_menus/mnu_sys_actions_clipboard.nss 
~ (Modified) exe/NSS/_5_menus/system_menus/mnu_sys_debug_commands.nss 
~ (Modified) exe/NSS/_5_menus/user_menus/mnu_actions_user_create.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_titlebar.nss 
``` 
 
| 
| 
 
## Commit: ce16267 - (5 months ago) nss:checkin - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_3dsmax.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
``` 
 
| 
| 
 
## Commit: f62b5cd - (5 months ago) nss:checkin - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_blender.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_libreoffice.nss 
~ (Modified) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_flow.nss 
~ (Modified) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_workflow.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
``` 
 
| 
| 
 
## Commit: 9f77a48 - (5 months ago) py venv bat - DSK 
 
### Changed Files: 
 
```diff 
+ (Added) exe/LIB/py_venv_pip_install.bat 
+ (Added) exe/LIB/py_venv_run_script.bat 
+ (Added) exe/LIB/py_venv_terminal.bat 
+ (Added) exe/LIB/py_venv_upgrade_requirements.bat 
+ (Added) exe/LIB/py_venv_write_requirements.bat 
+ (Added) exe/NSS/_3_items/user_scripts/itm_bat_pyvenvterminal.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_bat_pyvenvwriterequirements.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
``` 
 
| 
| 
 
## Commit: f626e72 - (5 months ago) nss:scratchpad (fixed keys) - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_scratchpad.nss 
``` 
 
| 
| 
 
## Commit: 12ee307 - (5 months ago) checkin - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_1_init/_a_defaults/def_uris.nss 
~ (Modified) exe/NSS/_5_menus/system_menus/mnu_apps_microsoft.nss 
~ (Modified) exe/NSS/_5_menus/user_menus/mnu_actions_user_create.nss 
``` 
 
| 
| 
 
## Commit: 2bb0007 - (5 months ago) nss:scratchpad:checkin + dirsandroid - DSK 
 
### Changed Files: 
 
```diff 
+ (Added) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_android.nss 
~ (Modified) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_cloud.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_scratchpad.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_dirs.nss 
``` 
 
| 
| 
 
## Commit: e1ae5ed - (5 months ago) nss:scratchpad (fixed keys to dir.parent for stp) - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_scratchpad.nss 
``` 
 
| 
| 
 
## Commit: d6b1a2c - (5 months ago) urls:checkin - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_4_groups/jorn_urls/grp_jorn_urls_gpts.nss 
``` 
 
| 
| 
 
## Commit: 673aeb6 - (5 months ago) mnu:scratchpad:wip - DSK 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_scratchpad.nss 
``` 
 
| 
| 
 
## Commit: 121d933 - (5 months ago) nss:checkin (scratchpad) - DSK 
 
### Changed Files: 
 
```diff 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_pdf24.nss 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_powertoys.nss 
~ (Modified) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_flow.nss 
+ (Added) exe/NSS/_5_menus/jorn_menus/mnu_scratchpad.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
~ (Modified) exe/__REGISTER.BAT 
``` 
 
| 
| 
 
## Commit: 2ce6b3b - (6 months ago) added keyboard modifiers to 3dsmax and blender - LAP 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_1_init/_a_defaults/def_keys.nss 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_3dsmax.nss 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_blender.nss 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_powertoys.nss 
``` 
 
| 
| 
 
## Commit: 8890cf1 - (6 months ago) checkin - LAP 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_apps.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
``` 
 
| 
| 
 
## Commit: 2e27688 - (6 months ago) nss:keys: - added keys for opening additional folders on special key combinations (useful for apps that are not placed in default locations) - LAP 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_1_init/_a_defaults/def_keys.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_powertoys.nss 
``` 
 
| 
| 
 
## Commit: ffa6a52 - (6 months ago) apps:visgrps - LAP 
 
### Changed Files: 
 
```diff 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_dirs.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
``` 
 
| 
| 
 
## Commit: 95f48cf - (6 months ago) wip - LAP 
 
### Changed Files: 
 
```diff 
+ (Added) .gitignore 
- (Deleted) __SHELL__.sublime-workspace 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_3dsmax.nss 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_blender.nss 
~ (Modified) exe/NSS/_3_items/user_apps/itm_app_telegram.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_windowutils.nss 
+ (Added) exe/NSS/_3_items/user_utils/itm_py_win4ro1.nss 
~ (Modified) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_flow.nss 
+ (Added) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_work.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_apps.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_dirs.nss 
~ (Modified) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_scripts.nss 
~ (Modified) exe/NSS/_5_menus/user_menus/mnu_actions_user_create.nss 
~ (Modified) exe/NSS/_5_menus/wip_menus/mnu_Processes.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_explorer.nss 
~ (Modified) exe/NSS/_6_contexts/ctx_taskbar.nss 
``` 
 
| 
| 
 
## Commit: 9e12805 - (6 months ago) init - DSK 
 
### Changed Files: 
 
```diff 
+ (Added) __SHELL__.sublime-project 
+ (Added) __SHELL__.sublime-workspace 
+ (Added) exe/.backups/nss_v1_backup_2024.08.31.7z 
+ (Added) exe/.gitignore 
+ (Added) exe/NSS/_1_init/_a_defaults/def_bools.nss 
+ (Added) exe/NSS/_1_init/_a_defaults/def_colors.nss 
+ (Added) exe/NSS/_1_init/_a_defaults/def_icons.nss 
+ (Added) exe/NSS/_1_init/_a_defaults/def_keys.nss 
+ (Added) exe/NSS/_1_init/_a_defaults/def_paths.nss 
+ (Added) exe/NSS/_1_init/_a_defaults/def_tooltips.nss 
+ (Added) exe/NSS/_1_init/_a_defaults/def_uris.nss 
+ (Added) exe/NSS/_1_init/_b_config/cfg_settings.nss 
+ (Added) exe/NSS/_1_init/_b_config/cfg_theme.nss 
+ (Added) exe/NSS/_1_init/_c_overrides/__update_everything64.nss 
+ (Added) exe/NSS/_1_init/_c_overrides/mod_icons.nss 
+ (Added) exe/NSS/_1_init/_c_overrides/mod_positions.nss 
+ (Added) exe/NSS/_1_init/_c_overrides/mod_visibility.nss 
+ (Added) exe/NSS/_1_init/_d_cleanup/cln_desktop.nss 
+ (Added) exe/NSS/_1_init/_d_cleanup/cln_explorer.nss 
+ (Added) exe/NSS/_1_init/_d_cleanup/cln_taskbar.nss 
+ (Added) exe/NSS/_2_variables/old/var_commands_cmd.nss 
+ (Added) exe/NSS/_2_variables/old/var_commands_ps1.nss 
+ (Added) exe/NSS/_2_variables/old/var_commands_py.nss 
+ (Added) exe/NSS/_2_variables/old/var_directories.nss 
+ (Added) exe/NSS/_2_variables/old/var_executables.nss 
+ (Added) exe/NSS/_2_variables/old/var_paths.nss 
+ (Added) exe/NSS/_3_items/registry_commands/itm_reg_setkeyboardrepeatrate.nss 
+ (Added) exe/NSS/_3_items/registry_commands/itm_reg_settaskbarleft.nss 
+ (Added) exe/NSS/_3_items/system_actions/itm_action_sys_copypath.nss 
+ (Added) exe/NSS/_3_items/system_actions/itm_action_sys_createtxt.nss 
+ (Added) exe/NSS/_3_items/system_actions/itm_action_sys_restartexplorer.nss 
+ (Added) exe/NSS/_3_items/system_actions/itm_action_sys_showdesktop.nss 
+ (Added) exe/NSS/_3_items/system_apps/itm_app_sys_calc.nss 
+ (Added) exe/NSS/_3_items/system_apps/itm_app_sys_cmd.nss 
+ (Added) exe/NSS/_3_items/system_apps/itm_app_sys_magnify.nss 
+ (Added) exe/NSS/_3_items/system_apps/itm_app_sys_mspaint.nss 
+ (Added) exe/NSS/_3_items/system_apps/itm_app_sys_notepad.nss 
+ (Added) exe/NSS/_3_items/system_apps/itm_app_sys_osk.nss 
+ (Added) exe/NSS/_3_items/system_apps/itm_app_sys_powershell.nss 
+ (Added) exe/NSS/_3_items/system_apps/itm_app_sys_powershellise.nss 
+ (Added) exe/NSS/_3_items/system_apps/itm_app_sys_regedit.nss 
+ (Added) exe/NSS/_3_items/system_apps/itm_app_sys_taskmanager.nss 
+ (Added) exe/NSS/_3_items/system_dirs/itm_dir_sys_appdata.nss 
+ (Added) exe/NSS/_3_items/system_dirs/itm_dir_sys_desktop.nss 
+ (Added) exe/NSS/_3_items/system_dirs/itm_dir_sys_documents.nss 
+ (Added) exe/NSS/_3_items/system_dirs/itm_dir_sys_downloads.nss 
+ (Added) exe/NSS/_3_items/system_dirs/itm_dir_sys_programfiles.nss 
+ (Added) exe/NSS/_3_items/system_dirs/itm_dir_sys_recent.nss 
+ (Added) exe/NSS/_3_items/system_dirs/itm_dir_sys_recyclebin.nss 
+ (Added) exe/NSS/_3_items/system_dirs/itm_dir_sys_startup.nss 
+ (Added) exe/NSS/_3_items/system_dirs/itm_dir_sys_thispc.nss 
+ (Added) exe/NSS/_3_items/system_dirs/itm_dir_sys_userprofile.nss 
+ (Added) exe/NSS/_3_items/system_dirs/itm_dir_sys_userprofile_ssh.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_audacity.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_beyondcompare.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_blender.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_bulkrenameutility.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_everything.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_filezilla.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_gitrestoremtime.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_gsmartcontrol.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_irfanview.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_kdenlive.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_losslesscut.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_nirsoft_regfromapp.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_nirsoft_registrychangesview.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_nirsoft_regscanner.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_nirsoft_winexplorer.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_notepad++.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_qbittorrent.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_rustdesk.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_sharex.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_simplewall.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_sourcetree.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_sublimetext.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_sysinternals_autoruns.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_sysinternals_diskmon.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_sysinternals_procexp.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_sysinternals_tcpview.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_telegram.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_vlc.nss 
+ (Added) exe/NSS/_3_items/user_apps/itm_app_winmerge.nss 
+ (Added) exe/NSS/_3_items/user_dirs/itm_dir_jorn_scratch.nss 
+ (Added) exe/NSS/_3_items/user_dirs/itm_dir_jorn_share.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_bookmarkfolderizer.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_environmentvariablesmanager.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_gitfilterrepo.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_gitsizeanalyzer.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_markdowngenerator.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_projectgenerator.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_sanitizefilenames.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_speechtotext.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_urlgenerator.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_videocompressor.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_youtubedownloader.nss 
+ (Added) exe/NSS/_3_items/user_scripts/itm_py_youtubeplaylistmanager.nss 
+ (Added) exe/NSS/_4_groups/jorn_actions/grp_actions_user_create.nss 
+ (Added) exe/NSS/_4_groups/jorn_actions/grp_actions_user_processes.nss 
+ (Added) exe/NSS/_4_groups/jorn_actions/grp_jorn_actions_git.nss 
+ (Added) exe/NSS/_4_groups/jorn_apps/grp_jorn_apps_nirsoft.nss 
+ (Added) exe/NSS/_4_groups/jorn_apps/grp_jorn_apps_sysinternals.nss 
+ (Added) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_git_portal.nss 
+ (Added) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_cloud.nss 
+ (Added) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_flow.nss 
+ (Added) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_networkdrives.nss 
+ (Added) exe/NSS/_4_groups/jorn_dirs/grp_jorn_dirs_nas_workflow.nss 
+ (Added) exe/NSS/_4_groups/jorn_urls/grp_jorn_urls_gpts.nss 
+ (Added) exe/NSS/_4_groups/jorn_urls/grp_jorn_urls_websites.nss 
+ (Added) exe/NSS/_4_groups/system_folders/grp_sys_dirs_cloud.nss 
+ (Added) exe/NSS/_4_groups/system_folders/grp_sys_dirs_common.nss 
+ (Added) exe/NSS/_4_groups/user_debug/grp_debug_user_shell.nss 
+ (Added) exe/NSS/_4_groups/user_debug/grp_sys_debug_commands.nss 
+ (Added) exe/NSS/_4_groups/user_debug/grp_sys_debug_tooltips.nss 
+ (Added) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_actions_git.nss 
+ (Added) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_apps.nss 
+ (Added) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_apps_7zip.nss 
+ (Added) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_apps_everything.nss 
+ (Added) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_apps_git.nss 
+ (Added) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_apps_nirsoft.nss 
+ (Added) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_dirs.nss 
+ (Added) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_dirs_common.nss 
+ (Added) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_scripts.nss 
+ (Added) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_scripts_python.nss 
+ (Added) exe/NSS/_5_menus/jorn_menus/mnu_user_jorn_urls.nss 
+ (Added) exe/NSS/_5_menus/jorn_menus/mnu_user_projects.nss 
+ (Added) exe/NSS/_5_menus/system_menus/mnu_apps_microsoft.nss 
+ (Added) exe/NSS/_5_menus/system_menus/mnu_sys_actions_clipboard.nss 
+ (Added) exe/NSS/_5_menus/system_menus/mnu_sys_actions_setpeferences.nss 
+ (Added) exe/NSS/_5_menus/system_menus/mnu_sys_actions_windows.nss 
+ (Added) exe/NSS/_5_menus/system_menus/mnu_sys_debug_commands.nss 
+ (Added) exe/NSS/_5_menus/system_menus/mnu_sys_dirs_common.nss 
+ (Added) exe/NSS/_5_menus/user_menus/mnu_actions_user_create.nss 
+ (Added) exe/NSS/_5_menus/user_menus/mnu_user_apps_shell.nss 
+ (Added) exe/NSS/_5_menus/wip_menus/grp_UserLibrary.nss 
+ (Added) exe/NSS/_5_menus/wip_menus/mnu_AppLibrary.nss 
+ (Added) exe/NSS/_5_menus/wip_menus/mnu_Debugging.nss 
+ (Added) exe/NSS/_5_menus/wip_menus/mnu_GitFolders.nss 
+ (Added) exe/NSS/_5_menus/wip_menus/mnu_IconLibrary.nss 
+ (Added) exe/NSS/_5_menus/wip_menus/mnu_Processes.nss 
+ (Added) exe/NSS/_5_menus/wip_menus/mnu_TMP.nss 
+ (Added) exe/NSS/_5_menus/wip_menus/mnu_recentpaths.nss 
+ (Added) exe/NSS/_6_contexts/ctx_desktop.nss 
+ (Added) exe/NSS/_6_contexts/ctx_everything64.nss 
+ (Added) exe/NSS/_6_contexts/ctx_explorer.nss 
+ (Added) exe/NSS/_6_contexts/ctx_taskbar.nss 
+ (Added) exe/NSS/_6_contexts/ctx_titlebar.nss 
+ (Added) exe/__REGISTER.BAT 
+ (Added) exe/__UNREGISTER.BAT 
+ (Added) exe/shell.dll 
+ (Added) exe/shell.exe 
+ (Added) exe/shell.nss 
+ (Added) user/.TODO.nss 
+ (Added) user/dev_utils/Nirsoft_ShellMenuView/shmnview-x64.zip 
+ (Added) user/dev_utils/Shell-main_sourcecode.zip 
+ (Added) user/dev_utils/Shell-main_sourcecode/.gitignore 
+ (Added) user/dev_utils/angusj_resourcehacker/homepage.url 
+ (Added) user/dev_utils/angusj_resourcehacker/resource_hacker.zip 
+ (Added) user/dev_utils/iMShare-main.7z 
+ (Added) user/dev_utils/iMShare-main/.gitignore 
+ (Added) user/dev_utils/shell32.dll.mui 
+ (Added) user/notes/2023.10.14_Windows-CLSID_gist.github.com.txt 
+ (Added) user/notes/MS-URI-Handlers.nss 
+ (Added) user/notes/nilesoft_everything_searches.txt.nss 
+ (Added) user/notes/nilesoft_shell_docs.md 
+ (Added) user/notes/nilesoft_shell_snippets.nss 
+ (Added) user/notes/readups.txt.py 
+ (Added) user/notes/shell_WM_COMMAND.nss 
+ (Added) user/notes/shell__EXAMPLES__.py.nss 
+ (Added) user/notes/shell__NOTES__.json.nss 
+ (Added) user/notes/shell__TESTS.nss 
+ (Added) user/notes/shell_git_discussions.txt 
+ (Added) user/notes/unsorted.nss 
+ (Added) user/refs/Uten navn.png.lnk 
+ (Added) user/refs/Uten navn2.png 
+ (Added) user/refs/file-manage.nss 
+ (Added) user/refs/goto.nss 
+ (Added) user/refs/images.nss 
+ (Added) user/refs/mediacenter-quick-settings.nss 
+ (Added) user/refs/nilesoft_discord.zip 
+ (Added) user/refs/nilesoft_discord/.gitignore 
+ (Added) user/refs/official_docs/configuration/index.html 
+ (Added) user/refs/official_docs/configuration/modify-items.html 
+ (Added) user/refs/official_docs/configuration/new-items.html 
+ (Added) user/refs/official_docs/configuration/properties.html 
+ (Added) user/refs/official_docs/configuration/settings.html 
+ (Added) user/refs/official_docs/configuration/themes.html 
+ (Added) user/refs/official_docs/examples/copy-path.html 
+ (Added) user/refs/official_docs/examples/favorites.html 
+ (Added) user/refs/official_docs/examples/goto.html 
+ (Added) user/refs/official_docs/examples/index.html 
+ (Added) user/refs/official_docs/examples/taskbar.html 
+ (Added) user/refs/official_docs/expressions/color.html 
+ (Added) user/refs/official_docs/expressions/comments.html 
+ (Added) user/refs/official_docs/expressions/identifier.html 
+ (Added) user/refs/official_docs/expressions/index.html 
+ (Added) user/refs/official_docs/expressions/numeric.html 
+ (Added) user/refs/official_docs/expressions/operators.html 
+ (Added) user/refs/official_docs/expressions/string.html 
+ (Added) user/refs/official_docs/expressions/variables.html 
+ (Added) user/refs/official_docs/functions/app.html 
+ (Added) user/refs/official_docs/functions/appx.html 
+ (Added) user/refs/official_docs/functions/clipboard.html 
+ (Added) user/refs/official_docs/functions/color.html 
+ (Added) user/refs/official_docs/functions/command.html 
+ (Added) user/refs/official_docs/functions/id.html 
+ (Added) user/refs/official_docs/functions/image.html 
+ (Added) user/refs/official_docs/functions/index.html 
+ (Added) user/refs/official_docs/functions/ini.html 
+ (Added) user/refs/official_docs/functions/input.html 
+ (Added) user/refs/official_docs/functions/io.html 
+ (Added) user/refs/official_docs/functions/key.html 
+ (Added) user/refs/official_docs/functions/msg.html 
+ (Added) user/refs/official_docs/functions/path.html 
+ (Added) user/refs/official_docs/functions/process.html 
+ (Added) user/refs/official_docs/functions/reg.html 
+ (Added) user/refs/official_docs/functions/regex.html 
+ (Added) user/refs/official_docs/functions/sel.html 
+ (Added) user/refs/official_docs/functions/str.html 
+ (Added) user/refs/official_docs/functions/sys.html 
+ (Added) user/refs/official_docs/functions/this.html 
+ (Added) user/refs/official_docs/functions/user.html 
+ (Added) user/refs/official_docs/functions/window.html 
+ (Added) user/refs/official_docs/get-started.html 
+ (Added) user/refs/official_docs/images/config.png 
+ (Added) user/refs/official_docs/images/copypath1.png 
+ (Added) user/refs/official_docs/images/copypath2.png 
+ (Added) user/refs/official_docs/images/copypath3.png 
+ (Added) user/refs/official_docs/images/dark/goto.png 
+ (Added) user/refs/official_docs/images/fav1.png 
+ (Added) user/refs/official_docs/images/fav2.png 
+ (Added) user/refs/official_docs/images/fav3.png 
+ (Added) user/refs/official_docs/images/goto.png 
+ (Added) user/refs/official_docs/images/helloworld.png 
+ (Added) user/refs/official_docs/images/light/goto.png 
+ (Added) user/refs/official_docs/index.html 
+ (Added) user/refs/official_docs/installation.html 
+ (Added) user/refs/official_docs/menu.json 
+ (Added) user/refs/reference.html 
+ (Added) user/refs/static.nss 
+ (Added) user/refs/taskbar.nss 
+ (Added) user/refs/terminal.nss 
+ (Added) user/refs/tmp_codedumping.nss 
+ (Added) user/refs/urls/How to Activate the dark and light mode through the registry #297.url 
+ (Added) user/refs/urls/Ziggle - A handy tool to lookup Win32 Constants.url 
+ (Added) user/refs/urls/Ziggle.zip 
+ (Added) user/refs/urls/fluenticons.co.url 
+ (Added) user/refs/urls/issues.github.com.url 
+ (Added) user/refs/urls/local.nss.key-modifiers.url 
+ (Added) user/refs/urls/nilesoft.org.docs.configuration.index.html.url 
+ (Added) user/refs/urls/nilesoft.org.docs.url 
+ (Added) user/refs/urls/nilesoft.org.url 
+ (Added) user/unsorted/Icons.zip 
+ (Added) user/unsorted/NSS - Icons Collection/icons/add_a_network_location.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/add_to_favorites.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/align_icons_to_grid.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/auto_arrange_icons.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/bitlocker.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/burn_disc_image.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/cleanup.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/code.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/collapse.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/command_prompt.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/compressed.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/content.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/copy.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/copy_path.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/copy_to.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/cortana.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/create_shortcut.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/customize_this_folder.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/cut.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/delete.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/desktop.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/details.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/device_manager.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/disconnect_network_drive.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/disk_management.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/display_settings.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/edit.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/eject.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/expand.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/extra_large_icons.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/file_explorer.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/file_explorer_options.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/filter.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/format.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/give_access_to.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/group_by.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/include_in_library.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/install.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/invert_selection.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/large_icons.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/list.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/make_available_offline.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/make_available_online.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/manage.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/map_network_drive.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/medium_icons.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/more_options.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/mount.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/move_to.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/new.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/new_file.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/new_folder.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/next_desktop_background.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/nvidia.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/open_folder.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/open_new_tab.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/open_new_window.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/open_spot_light.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/open_with.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/paste.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/paste_shortcut.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/pc.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/personalize.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/pin.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/powershell.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/print.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/properties.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/reddit.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/redo.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/refresh.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/remove_from_favorites.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/rename.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/restore.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/restore_previous_versions.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/rotate_left.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/rotate_right.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/run_as_administrator.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/run_as_different_user.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/select_all.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/select_none.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/send_to.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/set_as_background.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/settings.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/share.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/show_file_extensions.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/show_hidden_files.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/small_icons.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/sort_by.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/task_manager.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/troubleshoot_compatibility.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/undo.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/unpin.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/view.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/view2.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/window.nss 
+ (Added) user/unsorted/NSS - Icons Collection/icons/x.nss 
+ (Added) user/unsorted/NSS - Icons Collection/images.nss 
+ (Added) user/unsorted/Sublime Material Red.ico 
+ (Added) user/unsorted/cleanup.nss 
+ (Added) user/unsorted/coding-quick-settings.nss 
+ (Added) user/unsorted/file-manage.nss 
+ (Added) user/unsorted/filestructure_tests/2024.06.06__16.03.nss 
+ (Added) user/unsorted/filestructure_tests/2024.06.06__16.07.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_explorer.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_submenus/_apps_SysInternals.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_submenus/_apps_groups/__group_apps_sysinternals.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_submenus/_apps_groups/_apps_items/___item_apps_audacity.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_submenus/_apps_groups/_apps_items/___item_apps_blender.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_submenus/_apps_groups/_apps_items/___item_apps_bulkrenameutility.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_submenus/_apps_groups/_apps_items/___item_apps_cmd.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_submenus/_apps_groups/_apps_items/___item_apps_everything.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_submenus/_apps_groups/_apps_items/___item_apps_filezilla.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_submenus/_apps_groups/_apps_items/___item_apps_notepad++.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_submenus/_apps_groups/_apps_items/___item_apps_procexp.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_submenus/_apps_groups/_apps_items/___item_apps_simplewall.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_submenus/_apps_groups/_apps_items/___item_apps_sourcetree.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_submenus/_apps_groups/_apps_items/___item_apps_sublimetext.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_submenus/_apps_groups/_apps_items/___item_apps_winmerge.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/_apps_taskbar.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/structure_notes_001.nss 
+ (Added) user/unsorted/filestructure_tests/___apps/structure_notes_002.nss 
+ (Added) user/unsorted/filestructure_tests/chatgpt.com.c.c15d2b86-8599-45f5-b981-70e3fb2ebd9e.url 
+ (Added) user/unsorted/filestructure_tests/gemini.google.com.app.6db231fb809a6dc4.url 
+ (Added) user/unsorted/global_TODO.nss 
+ (Added) user/unsorted/gpt1.nss 
+ (Added) user/unsorted/grp_icons_list.nss 
+ (Added) user/unsorted/grp_scripts.nss 
+ (Added) user/unsorted/icon_notes.nss 
+ (Added) user/unsorted/images.nss 
+ (Added) user/unsorted/items2folder.ps1 
+ (Added) user/unsorted/modify.nss 
+ (Added) user/unsorted/msi.nss 
+ (Added) user/unsorted/nss.modes.README.md 
+ (Added) user/unsorted/nss.modes.generate.cmd 
+ (Added) user/unsorted/nss.types.README.md 
+ (Added) user/unsorted/nss.types.generate.cmd 
+ (Added) user/unsorted/nss_items_to_test/SevenZip.nss 
+ (Added) user/unsorted/nss_items_to_test/ShellNew.nss 
+ (Added) user/unsorted/nss_items_to_test/add_path.nss 
+ (Added) user/unsorted/nss_items_to_test/firewallrule.nss 
+ (Added) user/unsorted/nss_items_to_test/glyphs.nss 
+ (Added) user/unsorted/nss_items_to_test/icon-shell_fr2.nss 
+ (Added) user/unsorted/nss_items_to_test/icon_viewer.nss 
+ (Added) user/unsorted/nss_items_to_test/modifyES.nss 
+ (Added) user/unsorted/nss_items_to_test/ref_settings.nss 
+ (Added) user/unsorted/nss_items_to_test/ref_themes.nss 
+ (Added) user/unsorted/nss_items_to_test/showHiddenFilesOnDesktop.nss 
+ (Added) user/unsorted/nss_items_to_test/startallback.nss 
+ (Added) user/unsorted/nss_items_to_test/svg_icon_copilot.nss 
+ (Added) user/unsorted/nss_items_to_test/wallPaperEngine.nss 
+ (Added) user/unsorted/old/.backups/__RELOAD/__RELOAD__2024.01.20__001.BAT 
+ (Added) user/unsorted/old/.backups/__RELOAD/__RELOAD__2024.01.20__002.BAT 
+ (Added) user/unsorted/old/.backups/__RELOAD/__RELOAD__2024.01.20__003.BAT 
+ (Added) user/unsorted/old/.backups/__RELOAD/__RELOAD__2024.01.20__004.BAT 
+ (Added) user/unsorted/old/.backups/menu_clipboard/menu_clipboard__2024.01.26__001.nss 
+ (Added) user/unsorted/old/.backups/menu_clipboard/menu_clipboard__2024.01.26__002.nss 
+ (Added) user/unsorted/old/.backups/menu_common_clipboard/menu_common_clipboard__2024.01.26__001.nss 
+ (Added) user/unsorted/old/.backups/menu_new/menu_new__2024.01.29__001.nss 
+ (Added) user/unsorted/old/.backups/menu_processes/menu_processes__2024.01.27__001.nss 
+ (Added) user/unsorted/old/.backups/shell/shell__2024.01.22__001.nss 
+ (Added) user/unsorted/old/.backups/shell/shell__2024.01.24__001.nss 
+ (Added) user/unsorted/old/.backups/shell/shell__2024.01.24__002.nss 
+ (Added) user/unsorted/old/.backups/shell/shell__2024.01.24__003.nss 
+ (Added) user/unsorted/old/.backups/shell/shell__2024.01.24__004.nss 
+ (Added) user/unsorted/old/.backups/shell/shell__2024.01.24__005.nss 
+ (Added) user/unsorted/old/.backups/shell/shell__2024.01.24__006.nss 
+ (Added) user/unsorted/old/.backups/shell/shell__2024.01.24__007.nss 
+ (Added) user/unsorted/old/.backups/shell/shell__2024.01.24__008.nss 
+ (Added) user/unsorted/old/.backups/shell/shell__2024.01.24__009.nss 
+ (Added) user/unsorted/old/.backups/shell/shell__2024.01.24__010.nss 
+ (Added) user/unsorted/old/.backups/shell/shell__2024.01.24__011.nss 
+ (Added) user/unsorted/old/.backups/shell/shell__2024.01.24__012.nss 
+ (Added) user/unsorted/old/discarded/gpt_1.nss 
+ (Added) user/unsorted/old/discarded/nilesoft.shell.portable.install.bat 
+ (Added) user/unsorted/old/discarded/winget.nilesoft.shell.bat 
+ (Added) user/unsorted/old/nilesoftshell_chatgpt/2023.11.28_chatgpt.html 
+ (Added) user/unsorted/shell_dump.nss 
+ (Added) user/unsorted/shell_menus_external/custom-icons.nss 
+ (Added) user/unsorted/shell_menus_external/glifos-shell.nss 
+ (Added) user/unsorted/shell_menus_external/shell-icons.nss 
+ (Added) user/unsorted/shell_menus_external/theme-manager.nss 
+ (Added) user/unsorted/static.nss 
+ (Added) user/unsorted/todo/fileassoc.bat 
+ (Added) user/unsorted/todo/grp_develop.nss 
+ (Added) user/unsorted/todo/grp_goto.nss 
+ (Added) user/unsorted/todo/grp_terminal.nss 
+ (Added) user/unsorted/todo/grp_zip.nss 
+ (Added) user/unsorted/todo/m0nkrus.nfo 
+ (Added) user/unsorted/transfer.sh.nss 
+ (Added) versions/app_nilesoft-shell_v1.9.15.zip 
``` 
 
| 
| 
 
