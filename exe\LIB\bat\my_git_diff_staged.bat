:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__init_path__=%CD%"
SET "__base_name__=%~n0"
SET "__output_file__=!__init_path__!\diff.staged.md"

:: =============================================================================
:: git: verify repository
:: =============================================================================
:VerifyGitRepo
    git rev-parse --is-inside-work-tree > nul 2>&1
    IF ERRORLEVEL 1 (
        ECHO Error: Not a git repository.
        ECHO Please run this script from within a directory managed by Git.
        GOTO ExitScript
    )
    GOTO CreateDiffFile

:: =============================================================================
:: file: create diff file
:: =============================================================================
:CreateDiffFile
    ECHO Generating diff for staged changes...
    ECHO ```diff > "!__output_file__!"
    git diff --staged >> "!__output_file__!"
    IF ERRORLEVEL 1 (
        ECHO Error: Failed to generate diff.
        DEL "!__output_file__!" 2>nul
        GOTO ExitScript
    )
    ECHO.>> "!__output_file__!"
    ECHO ```>> "!__output_file__!"

    GOTO ReportCompletion

:: =============================================================================
:: report: show completion message
:: =============================================================================
:ReportCompletion
    ECHO.
    ECHO Success!
    ECHO Diff file for staged changes has been saved to: !__output_file__!
    ECHO.
    GOTO ExitScript

:: =============================================================================
:: cmd: exit
:: =============================================================================
:ExitScript
    ECHO. & ECHO Window will close in 3 seconds ...& PING 127.0.0.1 -n 3 > NUL
    ENDLOCAL
    EXIT /B
