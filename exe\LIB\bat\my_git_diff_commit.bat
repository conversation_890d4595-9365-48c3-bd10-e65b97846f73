:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__init_path__=%CD%"
SET "__base_name__=%~n0"

:: =============================================================================
:: git: verify repository
:: =============================================================================
:VerifyGitRepo
    git rev-parse --is-inside-work-tree > nul 2>&1
    IF ERRORLEVEL 1 (
        ECHO Error: Not a git repository.
        ECHO Please run this script from within a directory managed by Git.
        GOTO ExitScript
    )
    GOTO GetCommit

:: =============================================================================
:: git: prompt user for commit
:: =============================================================================
:GetCommit
    ECHO.
    ECHO Recent commits:
    git log --pretty=format:"%%h - %%s (%%cr)" -n 15
    ECHO.
    SET "target_commit="
    SET /P "target_commit=Enter the commit hash to diff against (or leave blank to exit): "
    IF "!target_commit!"=="" (
        ECHO No commit entered. Exiting.
        GOTO ExitScript
    )
    GOTO ValidateCommit

:: =============================================================================
:: git: validate commit
:: =============================================================================
:ValidateCommit
    git cat-file -e !target_commit! >nul 2>&1
    IF ERRORLEVEL 1 (
        ECHO.
        ECHO Error: '!target_commit!' is not a valid commit reference. Please try again.
        GOTO GetCommit
    )
    GOTO CreateDiffFile

:: =============================================================================
:: file: create diff file
:: =============================================================================
:CreateDiffFile
    FOR /F "tokens=*" %%a IN ('git rev-parse --short !target_commit!') DO SET "target_commit_short=%%a"
    SET "__output_file__=!__init_path__!\diff.!target_commit_short!.md"

    ECHO Generating diff against commit !target_commit_short!...
    ECHO ```diff > "!__output_file__!"
    git diff !target_commit! >> "!__output_file__!"
    IF ERRORLEVEL 1 (
        ECHO Error: Failed to generate diff.
        DEL "!__output_file__!" 2>nul
        GOTO ExitScript
    )
    ECHO.>> "!__output_file__!"
    ECHO ```>> "!__output_file__!"

    GOTO ReportCompletion

:: =============================================================================
:: report: show completion message
:: =============================================================================
:ReportCompletion
    ECHO.
    ECHO Success!
    ECHO Diff file against commit !target_commit_short! has been saved to: !__output_file__!
    ECHO.
    GOTO ExitScript

:: =============================================================================
:: cmd: exit
:: =============================================================================
:ExitScript
    ECHO. & ECHO Window will close in 3 seconds ...& PING 127.0.0.1 -n 3 > NUL
    ENDLOCAL
    EXIT /B
