:: =============================================================================
:: Extracts each commit from git history into separate directories using worktree
:: Creates incremental snapshots for testing multiple versions quickly
:: =============================================================================

@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION

:: Set working directory to script's location or provided path
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

:: =============================================================================
:: Git: Verify we are in a Git repository
:: =============================================================================
git rev-parse --is-inside-work-tree > nul 2>&1
IF ERRORLEVEL 1 (
    ECHO Error: Not a git repository.
    ECHO Please run this script from within a Git-managed directory.
    GOTO ExitScript
)

:: =============================================================================
:: Setup: Create commit snapshots directory
:: =============================================================================
ECHO.
ECHO Creating commit snapshots directory...
IF NOT EXIST "commit_snapshots" MKDIR "commit_snapshots"
CD /D "commit_snapshots"

:: =============================================================================
:: Git: Extract all commits and create worktrees
:: =============================================================================
ECHO.
ECHO Extracting commit history and creating snapshots...
ECHO.

SET "success_count=0"
SET "fail_count=0"
SET "counter=1"

:: Create temporary file to store commit hashes
SET "temp_file=%TEMP%\git_commits_%RANDOM%.txt"
git log --reverse --pretty=format:"%%H" > "%temp_file%" 2>nul

:: Process each commit
FOR /F "usebackq delims=" %%C IN ("%temp_file%") DO (
    SET "commit=%%C"
    SET "short=!commit:~0,8!"
    SET "dir_name=commit_!counter:~-3!_!short!"
    
    :: Pad counter to 3 digits
    IF !counter! LSS 10 SET "dir_name=commit_00!counter!_!short!"
    IF !counter! LSS 100 IF !counter! GEQ 10 SET "dir_name=commit_0!counter!_!short!"
    IF !counter! GEQ 100 SET "dir_name=commit_!counter!_!short!"

    ECHO -----------------------------------------------------------------------------
    ECHO Processing commit !counter!: !short!

    :: Try to create worktree
    git worktree add --detach -f "!dir_name!" "!commit!" > nul 2>&1

    IF ERRORLEVEL 1 (
        ECHO [ERROR] Failed to create: !dir_name!
        SET /A fail_count+=1
    ) ELSE (
        ECHO [SUCCESS] Created: !dir_name!
        SET /A success_count+=1
    )

    SET /A counter+=1
)

:: Clean up temporary file
IF EXIST "%temp_file%" DEL "%temp_file%" >nul 2>&1

ECHO -----------------------------------------------------------------------------

:: =============================================================================
:: Report: Show summary of extraction results
:: =============================================================================
ECHO.
ECHO Commit extraction complete.
ECHO.
ECHO Successfully created: !success_count! snapshot(s).
IF !fail_count! GTR 0 (
    ECHO Failed to create:    !fail_count! snapshot(s).
)
ECHO.

IF !success_count! GTR 0 (
    ECHO Snapshots are available in the 'commit_snapshots' directory.
    ECHO Each directory contains the full codebase at that commit.
) ELSE (
    ECHO No snapshots were created. Check for git repository issues.
)

:: =============================================================================
:: Exit
:: =============================================================================
:ExitScript
ECHO. & ECHO Window will close in 10 seconds... & PING 127.0.0.1 -n 10 > NUL
ENDLOCAL
PAUSE
EXIT /B