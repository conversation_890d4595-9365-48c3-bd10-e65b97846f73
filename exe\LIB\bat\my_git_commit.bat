:: =============================================================================
:: cmd: initialize
:: =============================================================================
@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")
SET "__init_path__=%CD%"
SET "__base_name__=%~n0"

:: =============================================================================
:: git: verify repository
:: =============================================================================
:VerifyGitRepo
    git rev-parse --is-inside-work-tree > nul 2>&1
    IF ERRORLEVEL 1 (
        ECHO Error: Not a git repository.
        ECHO Please run this script from within a directory managed by Git.
        GOTO ExitScript
    )
    GOTO VerifyStagedChanges

:: =============================================================================
:: git: verify staged changes
:: =============================================================================
:VerifyStagedChanges
    :: --quiet suppresses output, --exit-code makes it exit with 1 if there are changes
    git diff --staged --quiet --exit-code
    :: If ERRORLEVEL is 0, there are no staged changes.
    IF NOT ERRORLEVEL 1 (
        ECHO No staged changes to commit.
        GOTO ExitScript
    )
    GOTO PromptForCommitMessage

:: =============================================================================
:: prompt: get commit message
:: =============================================================================
:PromptForCommitMessage
    ECHO.
    SET "commit_message="
    SET /P "commit_message=Enter commit message: "

    :: Check if the user provided a message
    IF "!commit_message!"=="" (
        ECHO Error: Commit message cannot be empty. Aborting.
        GOTO ExitScript
    )
    GOTO ExecuteCommit

:: =============================================================================
:: git: execute commit
:: =============================================================================
:ExecuteCommit
    ECHO.
    ECHO Committing staged files...
    git commit -m "!commit_message!"
    IF ERRORLEVEL 1 (
        ECHO Error: Git commit failed.
        GOTO ExitScript
    )
    GOTO ReportCompletion

:: =============================================================================
:: report: show completion message
:: =============================================================================
:ReportCompletion
    ECHO.
    ECHO Success!
    ECHO Changes have been committed with the message: "!commit_message!"
    ECHO.
    GOTO ExitScript

:: =============================================================================
:: cmd: exit
:: =============================================================================
:ExitScript
    ECHO. & ECHO Window will close in 4 seconds ...& PING 127.0.0.1 -n 4 > NUL
    ENDLOCAL
    EXIT /B
