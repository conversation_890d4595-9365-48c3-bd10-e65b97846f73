:: =============================================================================
:: Stages each modified and untracked file individually to prevent one error
:: from stopping the entire process. Handles long filenames gracefully.
:: =============================================================================

@ECHO OFF
SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION

:: Set working directory to script's location or provided path
IF EXIST "%~1" (CD /D "%~1") ELSE (CD /D "%~dp0")

:: =============================================================================
:: Git: Verify we are in a Git repository
:: =============================================================================
git rev-parse --is-inside-work-tree > nul 2>&1
IF ERRORLEVEL 1 (
    ECHO Error: Not a git repository.
    ECHO Please run this script from within a Git-managed directory.
    GOTO ExitScript
)

:: =============================================================================
:: Git: Process all files that need staging (modified + untracked)
:: =============================================================================
ECHO.
ECHO Scanning for files to stage (modified and untracked)...
ECHO.

SET "success_count=0"
SET "fail_count=0"
SET "skipped_count=0"

:: Create a temporary file to store all files that need staging
SET "temp_file=%TEMP%\git_files_%RANDOM%.txt"

:: Get modified files
git ls-files -m > "%temp_file%" 2>nul

:: Get untracked files (excluding those in .gitignore)
git ls-files --others --exclude-standard >> "%temp_file%" 2>nul

:: Process each file individually
FOR /F "usebackq delims=" %%F IN ("%temp_file%") DO (
    ECHO -----------------------------------------------------------------------------
    ECHO Processing: "%%F"

    :: Check if filename is too long (Windows path limit is ~260 chars)
    SET "filepath=%%F"
    CALL :GetStringLength "!filepath!" filepath_length

    IF !filepath_length! GTR 240 (
        ECHO [SKIPPED] Filename too long ^(!filepath_length! chars^): "%%F"
        ECHO           Consider renaming this file to a shorter name.
        SET /A skipped_count+=1
    ) ELSE (
        :: Try to stage the file
        git add "%%F" 2>nul

        :: Check the result of the "git add" command
        IF ERRORLEVEL 1 (
            ECHO [ERROR] Failed to stage: "%%F"
            ECHO         This file will be skipped. The script will continue.
            SET /A fail_count+=1
        ) ELSE (
            ECHO [SUCCESS] Staged: "%%F"
            SET /A success_count+=1
        )
    )
)

:: Clean up temporary file
IF EXIST "%temp_file%" DEL "%temp_file%" >nul 2>&1

ECHO -----------------------------------------------------------------------------

:: =============================================================================
:: Report: Show a summary of what was staged and what failed
:: =============================================================================
ECHO.
ECHO Staging process complete.
ECHO.
ECHO Successfully staged: !success_count! file(s).
IF !fail_count! GTR 0 (
    ECHO Failed to stage:   !fail_count! file(s) (due to various errors).
)
IF !skipped_count! GTR 0 (
    ECHO Skipped files:     !skipped_count! file(s) (filename too long).
    ECHO.
    ECHO TIP: Consider renaming files with very long names to shorter alternatives.
)
ECHO.

:: Check if any files were staged
git diff --staged --quiet --exit-code
IF NOT ERRORLEVEL 1 (
    ECHO No new files were staged.
) ELSE (
    ECHO Files have been staged. You can now commit with: git commit -m "Your message"
)

:: =============================================================================
:: Helper function to get string length
:: =============================================================================
GOTO :SkipFunction
:GetStringLength
SET "str=%~1"
SET "len=0"
:loop
IF DEFINED str (
    SET "str=%str:~1%"
    SET /A len+=1
    GOTO loop
)
SET "%~2=%len%"
GOTO :EOF
:SkipFunction

:: =============================================================================
:: Exit
:: =============================================================================
:ExitScript
ECHO. & ECHO Window will close in 10 seconds... & PING 127.0.0.1 -n 10 > NUL
ENDLOCAL
PAUSE
EXIT /B